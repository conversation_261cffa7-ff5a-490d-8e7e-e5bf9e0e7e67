model/
*.swp
Makefile.Debug
Makefile.Release
Version.h
Update/Update_*.xml
*.bak
*~
*.autosave
*.qm
*.user
test.xml
.CCodec.h.kate-swp
.kdev4/
.kdev_include_paths
.vs/
CMakeSettings.json
build_
modul/
build-*/
build/
build_android/
build_unix_mingw/
build_windows_mingw/
build_unix/
build_windows_msvc/
*.ncb
.metadata/
debug/
bin/
*.class
.deps/
Makefile.in
aclocal.m4
config.guess
config.h
config.h.in
config.h.in~
config.log
config.status
config.sub
configure
depcomp
install-sh
libtool
ltmain.sh
missing
reachmonitor
stamp-h1
.deps/
Makefile.in
aclocal.m4
config.guess
config.h
config.h.in
config.h.in~
config.log
config.status
config.sub
configure
depcomp
install-sh
libtool
ltmain.sh
missing
stamp-h1
*.bak
*.bs
*.la
*.lo
*.ft
*.ft.1
*.made
*.o
*.obj
*.old
*.orig
*.out
*.pdb
*.rej
.libs/
Makefile
*.cdf
*.cache
*.obj
*.ilk
*.resources
*.tlb
*.tli
*.tlh
*.tmp
*.rsp
*.pgc
*.pgd
*.meta
*.tlog
*.manifest
*.res
*.pch
*.exp
*.idb
*.rep
*.xdc
*.pdb
*_manifest.rc
*.bsc
*.sbr
*.opensdf
*.sdf
*.suo
Debug/
release/
Release/
ipch/
rabbitim.kdev4
*.pro.user.*
Doxygen/
Doxyfile
android/local.properties
android/gradlew.*
android/gradle.properties
*.iml

# Compiled Object files
*.slo
*.lo
*.o
*.obj

# Precompiled Headers
*.gch
*.pch

# Compiled Dynamic libraries
*.so
*.dylib
*.dll

# Fortran module files
*.mod

# Compiled Static libraries
*.lai
*.la
*.a
*.lib

# Executables
*.exe
*.out
*.app

# Platform Specifics - auto generated files
PlatformSpecifics/Windows/*.rc

# Visual studio - project files
*.sln
*.suo
*.vcxproj
*.vcxproj.filters
*.vcxproj.user

# Visual Studio - Build Results
[Dd]ebug/
[Rr]elease/
[Mm]in[Ss]ize[Rr]el/
[Rr]el[Ww]ith[Dd]eb[Ii]nfo/

# Visual Studio - Browsing Database File
*.sdf
*.opensdf

#osx xcode
DerivedData/
*.DS_Store
*.build
*.xcodeproj

#CPACK related files
CPackConfig-*.cmake
_CPack_Packages/

#packages
*.tar.gz
*.zip

android/.gradle/
android/.idea/
android/android.iml
android/gradle/
android/gradlew

**/__pycache__
.vscode